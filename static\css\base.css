/* Modern CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors */
    --primary-color: #8b5cf6;
    --primary-dark: #7c3aed;
    --primary-light: #a78bfa;
    --secondary-color: #06d6a0;
    --secondary-dark: #059669;
    --danger-color: #f87171;
    --danger-dark: #ef4444;
    --warning-color: #fbbf24;
    --success-color: #34d399;

    /* Dark Background Colors */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: #1e1e3f;
    --bg-hover: #252547;

    /* Text Colors */
    --text-primary: #e2e8f0;
    --text-secondary: #94a3b8;
    --text-muted: #64748b;
    --text-accent: #a78bfa;

    /* Border Colors */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-accent: #8b5cf6;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.5);
    --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.3);

    /* Border Radius */
    --border-radius: 0.75rem;
    --border-radius-lg: 1rem;
    --border-radius-xl: 1.5rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    min-height: 100vh;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 214, 160, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem 0;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

header h1 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    position: relative;
    backdrop-filter: blur(10px);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

input, textarea, select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-primary);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

input::placeholder, textarea::placeholder {
    color: var(--text-muted);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2), var(--shadow-glow);
    transform: translateY(-1px);
    background: var(--bg-tertiary);
}

/* Button Styles */
.btn, button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0.25rem;
    position: relative;
    overflow: hidden;
}

.btn::before, button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.btn:hover::before, button:hover::before {
    left: 100%;
}

.btn-primary, button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover, button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    border-color: var(--primary-light);
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(6, 214, 160, 0.3);
}

.btn-danger, .discard-btn {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover, .discard-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(248, 113, 113, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #f59e0b 100%);
    color: var(--bg-primary);
    border-color: var(--warning-color);
    font-weight: 700;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(251, 191, 36, 0.3);
}

/* Success/Error Messages */
.message {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-weight: 500;
    border: 1px solid;
}

.message-success {
    background: rgba(52, 211, 153, 0.1);
    color: var(--success-color);
    border-color: var(--success-color);
}

.message-error, .error-message {
    background: rgba(248, 113, 113, 0.1);
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.message-warning {
    background: rgba(251, 191, 36, 0.1);
    color: var(--warning-color);
    border-color: var(--warning-color);
}

/* Navigation Styles */
nav {
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.nav-container a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.nav-container a:hover {
    color: var(--text-primary);
    background: var(--bg-hover);
    border-color: var(--border-secondary);
    transform: translateY(-1px);
}

.nav-container a:first-child {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-container a:last-child {
    margin-left: auto;
    color: var(--danger-color);
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-primary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: var(--border-radius);
    border: 2px solid var(--bg-secondary);
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-color) 100%);
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5), 0 0 10px rgba(139, 92, 246, 0.3);
}

::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--secondary-dark) 100%);
}

::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) var(--bg-secondary);
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1rem;
    }

    header h1 {
        font-size: 2rem;
    }
}
