<!DOCTYPE html>
<html>
<head>
    <title>Meme Content Generator</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Edit Memes</a>
            <a href="/review_audio">🎵 Audio</a>
            <a href="/review_videos">📹 Videos</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
        </div>
        {% endif %}

        <!-- Statistics Overview -->
        <h2>📊 Content Statistics</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_memes }}</div>
                <div class="stat-label">Total Memes</div>
                <div class="stat-description">Memes fetched and ready</div>
            </div>

            <div class="stat-card info">
                <div class="stat-number">{{ stats.memes_without_text }}</div>
                <div class="stat-label">Image-Only Videos</div>
                <div class="stat-description">Memes without text (3sec videos)</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_with_text }}</div>
                <div class="stat-label">Audio Videos</div>
                <div class="stat-description">Memes with text and narration</div>
            </div>

            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_videos }}</div>
                <div class="stat-label">Videos Created</div>
                <div class="stat-description">Generated video content</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.memes_today }}</div>
                <div class="stat-label">Memes Today</div>
                <div class="stat-description">Fetched today</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.videos_today }}</div>
                <div class="stat-label">Videos Today</div>
                <div class="stat-description">Created today</div>
            </div>
        </div>



        <!-- Workflow Status -->
        {% if stats.total_memes > 0 %}
        <h2>📈 Workflow Progress</h2>
        <div class="workflow-progress">
            <div class="workflow-header">
                <span class="workflow-title">Content Pipeline Status</span>
                <span class="workflow-percentage">{{ ((stats.total_videos / stats.total_memes) * 100) | round(1) if stats.total_memes > 0 else 0 }}% Complete</span>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ ((stats.total_videos / stats.total_memes) * 100) if stats.total_memes > 0 else 0 }}%;"></div>
                </div>
            </div>
            <div class="workflow-details">
                {{ stats.total_memes }} memes → {{ stats.total_videos }} videos ({{ stats.memes_with_text }} with audio, {{ stats.memes_without_text }} image-only)
            </div>
        </div>
        {% endif %}

        <!-- Additional Stats -->
        {% if stats.discarded_memes > 0 or stats.discarded_videos > 0 %}
        <h2>🗑️ Discarded Content</h2>
        <div class="stats-grid">
            {% if stats.discarded_memes > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_memes }}</div>
                <div class="stat-label">Discarded Memes</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
            {% if stats.discarded_videos > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_videos }}</div>
                <div class="stat-label">Discarded Videos</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
