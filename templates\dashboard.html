<!DOCTYPE html>
<html>
<head>
    <title>Meme Content Generator</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Edit Memes</a>
            <a href="/workflow_audio">🎵 Audio</a>
            <a href="/review_videos">📹 Videos</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
        </div>
        {% endif %}

        <!-- Enhanced Dashboard Header -->
        <div class="dashboard-header">
            <h1 class="dashboard-title">🎭 Meme Video Generator</h1>
            <p class="dashboard-subtitle">Your complete content creation hub - from memes to viral videos</p>
        </div>

        <!-- Workflow Statistics -->
        <h2>🔄 Workflow Progress</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_memes }}</div>
                <div class="stat-label">Total Memes</div>
                <div class="stat-description">Fetched from Reddit</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-number">{{ stats.memes_pending_text }}</div>
                <div class="stat-label">Pending Text Review</div>
                <div class="stat-description">Awaiting text addition & approval</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_text_approved }}</div>
                <div class="stat-label">Text Approved</div>
                <div class="stat-description">Ready for audio phase</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-number">{{ stats.memes_pending_audio }}</div>
                <div class="stat-label">Pending Audio Review</div>
                <div class="stat-description">Text memes awaiting audio approval</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_audio_approved }}</div>
                <div class="stat-label">Audio Approved</div>
                <div class="stat-description">Ready for video generation</div>
            </div>

            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_videos }}</div>
                <div class="stat-label">Videos Created</div>
                <div class="stat-description">Final generated content</div>
            </div>
        </div>

        <!-- Content Type Breakdown -->
        <h2>📊 Content Types</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.memes_without_text }}</div>
                <div class="stat-label">Image-Only Videos</div>
                <div class="stat-description">Memes without text (3sec videos)</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_with_text }}</div>
                <div class="stat-label">Audio Videos</div>
                <div class="stat-description">Memes with text and narration</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.memes_today }}</div>
                <div class="stat-label">Memes Today</div>
                <div class="stat-description">Fetched today</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.videos_today }}</div>
                <div class="stat-label">Videos Today</div>
                <div class="stat-description">Created today</div>
            </div>
        </div>



        <!-- Subreddit Breakdown -->
        {% if stats.subreddit_stats %}
        <h2>📂 Subreddit Progress</h2>
        <div class="subreddit-stats">
            {% for subreddit, data in stats.subreddit_stats.items() %}
            <div class="subreddit-card">
                <h3>r/{{ subreddit }}</h3>
                <div class="subreddit-progress">
                    <div class="progress-step">
                        <span class="step-number">{{ data.total }}</span>
                        <span class="step-label">Fetched</span>
                    </div>
                    <div class="progress-arrow">→</div>
                    <div class="progress-step">
                        <span class="step-number">{{ data.text_approved }}</span>
                        <span class="step-label">Text Approved</span>
                    </div>
                    <div class="progress-arrow">→</div>
                    <div class="progress-step">
                        <span class="step-number">{{ data.audio_approved }}</span>
                        <span class="step-label">Audio Approved</span>
                    </div>
                    <div class="progress-arrow">→</div>
                    <div class="progress-step">
                        <span class="step-number">{{ data.videos }}</span>
                        <span class="step-label">Videos</span>
                    </div>
                </div>
                <div class="subreddit-percentage">
                    {{ ((data.videos / data.total) * 100) | round(1) if data.total > 0 else 0 }}% Complete
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Overall Workflow Status -->
        {% if stats.total_memes > 0 %}
        <h2>📈 Overall Pipeline Status</h2>
        <div class="workflow-progress">
            <div class="workflow-header">
                <span class="workflow-title">Content Pipeline Completion</span>
                <span class="workflow-percentage">{{ ((stats.total_videos / stats.total_memes) * 100) | round(1) if stats.total_memes > 0 else 0 }}% Complete</span>
            </div>
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ ((stats.total_videos / stats.total_memes) * 100) if stats.total_memes > 0 else 0 }}%;"></div>
                </div>
            </div>
            <div class="workflow-details">
                {{ stats.total_memes }} memes → {{ stats.total_videos }} videos ({{ stats.memes_with_text }} with audio, {{ stats.memes_without_text }} image-only)
            </div>
        </div>
        {% endif %}

        <!-- Additional Stats -->
        {% if stats.discarded_memes > 0 or stats.discarded_videos > 0 %}
        <h2>🗑️ Discarded Content</h2>
        <div class="stats-grid">
            {% if stats.discarded_memes > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_memes }}</div>
                <div class="stat-label">Discarded Memes</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
            {% if stats.discarded_videos > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_videos }}</div>
                <div class="stat-label">Discarded Videos</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
