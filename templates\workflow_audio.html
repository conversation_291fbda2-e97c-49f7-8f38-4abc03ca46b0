<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Review Phase</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Edit Memes</a>
            <a href="/workflow_audio">🎵 Audio</a>
            <a href="/review_videos">📹 Videos</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
        </div>
        {% endif %}

        <h1>🎵 Audio Review Phase</h1>
        <p>Generate audio for memes with text, then review and approve them for video generation.</p>

        {% if memes_by_subreddit %}
            <!-- Bulk Audio Generation -->
            <div class="bulk-actions">
                <form method="POST" action="/generate_audio" style="display: inline;">
                    <input type="hidden" name="generate_all" value="true">
                    <button type="submit" class="btn btn-primary">🎵 Generate Audio for All Memes</button>
                </form>
            </div>

            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ memes|length }} memes with text)</h2>

                {% for meme in memes %}
                {% if meme.text and meme.text.strip() %}
                <div class="meme-editor">
                    <div class="meme-image">
                        <img src="{{ meme.url }}" alt="Meme Image"
                             onclick="openImageModal(this.src)"
                             style="max-width: 400px; max-height: 300px; object-fit: contain; cursor: pointer;">
                    </div>
                    <div class="meme-text-editor">
                        <div class="meme-status">
                            {% if meme.audio_approved %}
                                <span class="status-approved">✅ Audio approved for video generation</span>
                            {% else %}
                                <span class="status-pending">⏳ Pending audio approval</span>
                            {% endif %}
                        </div>

                        <div class="meme-text-content">
                            <h4>Text for narration:</h4>
                            <p class="meme-text">{{ meme.text }}</p>
                        </div>

                        <div class="audio-preview">
                            {% set audio_path = session.get('audio_path_' + meme.id|string) %}
                            {% if audio_path %}
                                <h4>Generated Audio:</h4>
                                <audio controls style="width: 100%; margin-bottom: 1rem;">
                                    <source src="/{{ audio_path }}" type="audio/wav">
                                    Your browser does not support the audio tag.
                                </audio>
                            {% else %}
                                <p><em>No audio generated yet. Click "Generate Audio" to create audio for this meme.</em></p>
                            {% endif %}
                        </div>

                        <div class="workflow-actions">
                            {% if not audio_path %}
                            <form method="POST" action="/generate_audio" style="display: inline;">
                                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                                <button type="submit" class="btn btn-secondary">🎵 Generate Audio</button>
                            </form>
                            {% endif %}

                            {% if audio_path and not meme.audio_approved %}
                            <form method="POST" action="/approve_meme_audio" style="display: inline;">
                                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                                <button type="submit" class="btn btn-success">✅ Approve Audio & Continue</button>
                            </form>
                            {% endif %}

                            <form method="POST" action="/discard_meme" style="display: inline;">
                                <input type="hidden" name="meme_id" value="{{ meme.id }}">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('Are you sure you want to discard this meme?')">🗑️ Discard</button>
                            </form>
                        </div>
                    </div>
                </div>
                <hr>
                {% endif %}
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/review_memes" class="btn btn-secondary">⬅️ Back to Text Phase</a>
                <a href="/generate_videos" class="btn btn-secondary">🎬 Generate Videos from Approved Content</a>
                <a href="/review_videos" class="btn btn-secondary">📹 Continue to Video Review</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No memes with text are ready for audio review.
               <a href="/review_memes" class="btn btn-primary">📝 Go to Text Phase</a> to approve memes first!</p>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
