/* Configuration Styles */
.current-config {
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--gray-200);
}

.current-config h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.current-config p {
    margin-bottom: 0.5rem;
    color: var(--gray-600);
}

/* Subreddit Styles */
.subreddit-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: background-color 0.2s ease;
}

.subreddit-item:hover {
    background-color: var(--gray-100);
}

.subreddit-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.subreddit-link:hover {
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
}

.add-subreddit {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    border: 2px dashed var(--gray-300);
}

.add-subreddit input {
    margin-bottom: 0.5rem;
}

.current-subreddits {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.subreddit-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.subreddit-tag:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.subreddit-tag .subreddit-link {
    color: white;
    text-decoration: none;
}

.subreddit-tag .subreddit-link:hover {
    color: white;
    background-color: transparent;
}

/* Configuration Form */
.config-form {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.config-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.config-section h3 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-section-icon {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.config-description {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* Range Input Styles */
.range-input-container {
    margin: 1rem 0;
}

.range-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.range-input::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.range-value {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-left: 1rem;
}

/* Subreddit Selection */
.subreddit-selection {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.subreddit-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.subreddit-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    cursor: pointer;
}

.subreddit-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.subreddit-option input[type="checkbox"] {
    margin: 0;
}

.subreddit-option label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    text-transform: none;
    letter-spacing: normal;
    font-size: 0.875rem;
}

/* Custom Subreddit Input */
.custom-subreddit {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: white;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    text-align: center;
    transition: all 0.3s ease;
}

.custom-subreddit:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.custom-subreddit-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--gray-600);
    font-weight: 500;
}

.custom-subreddit-icon {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.custom-input-group {
    display: flex;
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.custom-input-group input {
    flex: 1;
    margin: 0;
}

.custom-input-group button {
    margin: 0;
    white-space: nowrap;
}

/* Configuration Preview */
.config-preview {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-top: 2rem;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--gray-800);
    font-weight: 600;
}

.preview-icon {
    color: var(--secondary-color);
    font-size: 1.25rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.preview-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.preview-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.preview-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Save Actions */
.save-actions {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-top: 2rem;
    border: 1px solid var(--gray-200);
}

.save-actions .btn {
    margin: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subreddit-grid {
        grid-template-columns: 1fr;
    }

    .custom-input-group {
        flex-direction: column;
    }

    .preview-grid {
        grid-template-columns: 1fr;
    }

    .save-actions .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }

    .current-subreddits {
        justify-content: center;
    }
}
