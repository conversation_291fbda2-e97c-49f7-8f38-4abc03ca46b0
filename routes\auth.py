from flask import Blueprint, render_template, request, redirect, url_for, session
from models import User

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/')
def home():
    """Home page redirects to login"""
    return render_template('login.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username, password=password).first()
        if user:
            session['user_id'] = user.id
            return redirect(url_for('dashboard.dashboard'))
        else:
            return render_template('login.html', error='Invalid email or password. Please try again.')
    return render_template('login.html')

@auth_bp.route('/logout')
def logout():
    """Handle user logout"""
    session.pop('user_id', None)
    return redirect(url_for('auth.login'))
