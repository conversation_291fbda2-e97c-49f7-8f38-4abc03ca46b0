<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Videos</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/videos.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Edit Memes</a>
            <a href="/workflow_audio">🎵 Audio</a>
            <a href="/review_videos">📹 Videos</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h1>📹 Final Video Review</h1>
        <p>Review and download your generated videos organized by subreddit.</p>

        {% if videos_by_subreddit %}
            {% for subreddit, videos in videos_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ videos|length }} videos)</h2>

                {% for video in videos %}
                <div class="video-item">
                    <div class="video-player">
                        <video controls style="max-width: 600px; max-height: 400px;">
                            <source src="/{{ video.video_path }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <div class="video-info">
                        <p><strong>Type:</strong>
                            {% if video.text and video.text.strip() %}
                                🎵 Video with Audio Narration
                            {% else %}
                                🖼️ Image-Only Video (3 seconds)
                            {% endif %}
                        </p>
                        {% if video.text and video.text.strip() %}
                        <p><strong>Text:</strong> {{ video.text }}</p>
                        {% else %}
                        <p><strong>Text:</strong> <em>No text (image-only video)</em></p>
                        {% endif %}
                        <p><strong>Created:</strong> {{ video.created_at.strftime('%Y-%m-%d %H:%M') if video.created_at else 'Unknown' }}</p>
                        <div class="video-actions">
                            <form method="POST" action="/discard_video" style="display: inline;">
                                <input type="hidden" name="video_id" value="{{ video.id }}">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('Are you sure you want to discard this video?')">🗑️ Discard</button>
                            </form>
                            {% if video.audio_path %}
                            <a href="/{{ video.audio_path }}" download class="btn btn-secondary">🎵 Download Audio</a>
                            {% endif %}
                            <a href="/{{ video.video_path }}" download class="btn btn-primary">📹 Download Video</a>
                        </div>
                    </div>
                </div>
                <hr>
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/workflow_audio" class="btn btn-secondary">⬅️ Back to Audio Phase</a>
                <a href="/generate_videos" class="btn btn-secondary">🎬 Generate More Videos</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No videos generated yet.
               <a href="/review_memes" class="btn btn-primary">📝 Start with Text Phase</a> →
               <a href="/workflow_audio" class="btn btn-secondary">🎵 Audio Phase</a> →
               <a href="/generate_videos" class="btn btn-secondary">🎬 Generate Videos</a>!</p>
        {% endif %}
    </div>
</body>
</html>
