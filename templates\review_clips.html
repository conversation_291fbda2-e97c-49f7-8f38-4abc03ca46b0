<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Clips</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/videos.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/review_memes">✏️ Edit Memes</a>
            <a href="/workflow_audio">🎵 Audio</a>
            <a href="/review_clips">🎬 Clips</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h1>🎬 Final Clip Review</h1>
        <p>Review and download your generated clips organized by subreddit.</p>

        {% if clips_by_subreddit %}
            {% for subreddit, clips in clips_by_subreddit.items() %}
            <div class="subreddit-section">
                <h2>📂 r/{{ subreddit }} ({{ clips|length }} clips)</h2>

                {% for clip in clips %}
                <div class="video-item">
                    <div class="video-player">
                        <video controls style="max-width: 600px; max-height: 400px;">
                            <source src="/{{ clip.clip_path }}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <div class="video-info">
                        <p><strong>Type:</strong>
                            {% if clip.text and clip.text.strip() %}
                                🎵 Clip with Audio Narration
                            {% else %}
                                🖼️ Image-Only Clip (3 seconds)
                            {% endif %}
                        </p>
                        {% if clip.text and clip.text.strip() %}
                        <p><strong>Text:</strong> {{ clip.text }}</p>
                        {% else %}
                        <p><strong>Text:</strong> <em>No text (image-only clip)</em></p>
                        {% endif %}
                        <p><strong>Created:</strong> {{ clip.created_at.strftime('%Y-%m-%d %H:%M') if clip.created_at else 'Unknown' }}</p>
                        <div class="video-actions">
                            <form method="POST" action="/discard_clip" style="display: inline;">
                                <input type="hidden" name="clip_id" value="{{ clip.id }}">
                                <button type="submit" class="btn btn-danger"
                                        onclick="return confirm('Are you sure you want to discard this clip?')">🗑️ Discard</button>
                            </form>
                            {% if clip.audio_path %}
                            <a href="/{{ clip.audio_path }}" download class="btn btn-secondary">🎵 Download Audio</a>
                            {% endif %}
                            <a href="/{{ clip.clip_path }}" download class="btn btn-primary">🎬 Download Clip</a>
                        </div>
                    </div>
                </div>
                <hr>
                {% endfor %}
            </div>
            {% endfor %}

            <div class="actions">
                <a href="/workflow_audio" class="btn btn-secondary">⬅️ Back to Audio Phase</a>
                <a href="/generate_clips" class="btn btn-secondary">🎬 Generate More Clips</a>
                <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
            </div>
        {% else %}
            <p>No clips generated yet.
               <a href="/review_memes" class="btn btn-primary">📝 Start with Text Phase</a> →
               <a href="/workflow_audio" class="btn btn-secondary">🎵 Audio Phase</a> →
               <a href="/generate_clips" class="btn btn-secondary">🎬 Generate Clips</a>!</p>
        {% endif %}
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        function openImageModal(src) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.classList.add('show');
            modalImg.src = src;
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
        }

        // Close modal when clicking outside the image
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
