from flask_sqlalchemy import SQLAlchemy

# This will be initialized in app.py
db = SQLAlchemy()

class User(db.Model):
    """User model for authentication"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(150), nullable=False, unique=True)
    password = db.Column(db.String(150), nullable=False)

class Meme(db.Model):
    """Meme model for storing meme data"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('user.id'), nullable=False)
    url = db.Column(db.String(500), nullable=False)  # Original Reddit URL
    image_path = db.Column(db.String(500))  # Local image file path
    text = db.Column(db.Text)
    subreddit = db.Column(db.String(100), nullable=False)  # Source subreddit
    discarded = db.Column(db.Boolean, default=False)

    # Workflow status tracking
    text_approved = db.Column(db.Boolean, default=False)  # Text phase approval
    audio_approved = db.Column(db.Boolean, default=False)  # Audio phase approval
    video_approved = db.Column(db.Boolean, default=False)  # Final video approval

    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

class Configuration(db.Model):
    """Configuration model for user settings"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    max_memes = db.Column(db.Integer, default=10)
    subreddits = db.Column(db.Text, default='memes')

class Clip(db.Model):
    """Clip model for storing generated clips"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    meme_id = db.Column(db.Integer, db.ForeignKey('meme.id'), nullable=False)
    clip_path = db.Column(db.String(500), nullable=False)
    audio_path = db.Column(db.String(500))
    text = db.Column(db.Text)
    discarded = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
