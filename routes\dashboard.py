from flask import Blueprint, render_template, session
from models import Meme, Video
from services.helpers import require_login
from sqlalchemy import func

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/dashboard')
@require_login
def dashboard():
    """Main dashboard page with statistics"""
    user_id = session['user_id']

    # Calculate statistics
    stats = {}

    # Total memes fetched
    stats['total_memes'] = Meme.query.filter_by(user_id=user_id, discarded=False).count()

    # Memes without text (will create image-only videos)
    stats['memes_without_text'] = Meme.query.filter_by(user_id=user_id, discarded=False).filter(
        (Meme.text == None) | (Meme.text == '')
    ).count()

    # Memes with text (will create videos with audio)
    stats['memes_with_text'] = Meme.query.filter_by(user_id=user_id, discarded=False).filter(
        (Meme.text != None) & (Meme.text != '')
    ).count()

    # All memes are ready for video generation (with or without audio)
    stats['memes_ready_for_video'] = stats['total_memes']

    # Total videos created
    stats['total_videos'] = Video.query.filter_by(user_id=user_id, discarded=False).count()

    # Discarded memes
    stats['discarded_memes'] = Meme.query.filter_by(user_id=user_id, discarded=True).count()

    # Discarded videos
    stats['discarded_videos'] = Video.query.filter_by(user_id=user_id, discarded=True).count()

    # Recent activity - memes added today
    from datetime import datetime
    today = datetime.now().date()
    stats['memes_today'] = Meme.query.filter_by(user_id=user_id, discarded=False).filter(
        func.date(Meme.created_at) == today
    ).count()

    # Recent activity - videos created today
    stats['videos_today'] = Video.query.filter_by(user_id=user_id, discarded=False).filter(
        func.date(Video.created_at) == today
    ).count()

    return render_template('dashboard.html', stats=stats)
