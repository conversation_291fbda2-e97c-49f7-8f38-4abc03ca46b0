import pyttsx3
import os
import logging

def generate_audio_from_text(text, output_path):
    """Generate enhanced audio file from text using TTS with improved settings"""
    try:
        # Initialize TTS engine
        engine = pyttsx3.init()

        # Enhanced audio properties for better quality
        # Reduce speech rate by 12% for more natural speech (150 -> 132)
        engine.setProperty('rate', 132)  # Slower, more natural speech rate

        # Increase volume by 25% for louder, clearer audio (0.9 -> 1.0 max)
        engine.setProperty('volume', 1.0)  # Maximum volume for clarity

        # Try to find and set the best available voice
        voices = engine.getProperty('voices')
        selected_voice = None

        if voices and len(voices) > 0:
            # Prefer female voices or voices with better quality indicators
            for voice in voices:
                voice_name = voice.name.lower() if voice.name else ""
                # Look for higher quality voices (often contain "enhanced", "premium", or specific names)
                if any(keyword in voice_name for keyword in ['zira', 'hazel', 'enhanced', 'premium', 'natural']):
                    selected_voice = voice
                    break

            # If no preferred voice found, use the first available
            if not selected_voice:
                selected_voice = voices[0]

            engine.setProperty('voice', selected_voice.id)
            logging.info(f"Using voice: {selected_voice.name}")

            # Try to set additional voice properties for more natural speech
            try:
                # Some engines support pitch adjustment
                if hasattr(engine, 'setProperty'):
                    # Slightly lower pitch for more pleasant sound
                    engine.setProperty('pitch', 0.8)  # Range typically 0.5-2.0
            except:
                pass  # Not all engines support pitch adjustment

        # Clean up and enhance the text for better speech
        clean_text = text.strip()
        if not clean_text:
            clean_text = "No text provided for this meme."

        # Enhance text for better TTS pronunciation
        clean_text = enhance_text_for_speech(clean_text)

        # Ensure the text is not too long
        if len(clean_text) > 500:
            clean_text = clean_text[:500] + "..."

        logging.info(f"Generating enhanced audio for text: {clean_text[:50]}...")

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Generate audio file
        engine.save_to_file(clean_text, output_path)
        engine.runAndWait()

        # Verify file was created and has reasonable size
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:  # At least 1KB
            logging.info(f"Enhanced audio file created successfully: {output_path} ({os.path.getsize(output_path)} bytes)")
            return True
        else:
            logging.error(f"Audio file was not created or is too small: {output_path}")
            return False

    except Exception as e:
        logging.error(f"Error generating enhanced audio: {e}")
        return False

def enhance_text_for_speech(text):
    """Enhance text for better TTS pronunciation and naturalness"""
    # Add pauses for better speech rhythm
    enhanced_text = text

    # Add slight pauses after punctuation for more natural speech
    enhanced_text = enhanced_text.replace('.', '. ')
    enhanced_text = enhanced_text.replace(',', ', ')
    enhanced_text = enhanced_text.replace('!', '! ')
    enhanced_text = enhanced_text.replace('?', '? ')

    # Replace common internet abbreviations with full words
    replacements = {
        'lol': 'laugh out loud',
        'omg': 'oh my god',
        'wtf': 'what the f',
        'tbh': 'to be honest',
        'imo': 'in my opinion',
        'btw': 'by the way',
        'fyi': 'for your information',
        'asap': 'as soon as possible',
        'etc': 'etcetera',
        '&': 'and',
        '@': 'at',
        'u': 'you',
        'ur': 'your',
        'r': 'are',
        'n': 'and',
        'w/': 'with',
        'w/o': 'without'
    }

    # Apply replacements (case insensitive)
    for abbrev, full_form in replacements.items():
        enhanced_text = enhanced_text.replace(abbrev.lower(), full_form)
        enhanced_text = enhanced_text.replace(abbrev.upper(), full_form)
        enhanced_text = enhanced_text.replace(abbrev.capitalize(), full_form)

    # Clean up multiple spaces
    enhanced_text = ' '.join(enhanced_text.split())

    return enhanced_text
