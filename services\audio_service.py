import pyttsx3
import os
import logging

def generate_audio_from_text(text, output_path):
    """Generate audio file from text using TTS"""
    try:
        # Initialize TTS engine
        engine = pyttsx3.init()

        # Set properties for better quality
        engine.setProperty('rate', 150)  # Speed of speech (words per minute)
        engine.setProperty('volume', 0.9)  # Volume level (0.0 to 1.0)

        # Try to set a better voice if available
        voices = engine.getProperty('voices')
        if voices and len(voices) > 0:
            # Use the first available voice
            engine.setProperty('voice', voices[0].id)
            logging.info(f"Using voice: {voices[0].name}")

        # Clean up the text
        clean_text = text.strip()
        if not clean_text:
            clean_text = "No text provided for this meme."

        # Ensure the text is not too long
        if len(clean_text) > 500:
            clean_text = clean_text[:500] + "..."

        logging.info(f"Generating audio for text: {clean_text[:50]}...")

        # Generate audio file
        engine.save_to_file(clean_text, output_path)
        engine.runAndWait()

        # Verify file was created
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            logging.info(f"Audio file created successfully: {output_path}")
            return True
        else:
            logging.error(f"Audio file was not created or is empty: {output_path}")
            return False

    except Exception as e:
        logging.error(f"Error generating audio: {e}")
        return False
