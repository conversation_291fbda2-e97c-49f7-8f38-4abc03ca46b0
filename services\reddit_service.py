import praw
import os
from dotenv import load_dotenv

load_dotenv()

def get_top_memes(limit=100, subreddits=None):
    """Fetch top memes from specified subreddits"""
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    username = os.getenv('REDDIT_USERNAME')

    if not all([client_id, client_secret, username]):
        return []

    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=f'AutomaticMemeContentGenerator/0.1 by {username}'
        )
    except Exception:
        return []

    memes = []
    if not subreddits:
        subreddits = ['memes']

    for subreddit_name in subreddits:
        try:
            subreddit = reddit.subreddit(subreddit_name)
            for submission in subreddit.top(time_filter='day', limit=limit):
                if submission.url.endswith(('jpg', 'jpeg', 'png', 'gif', 'webp')):
                    memes.append({
                        'url': submission.url,
                        'title': submission.title,
                        'subreddit': subreddit_name,
                        'score': submission.score,
                        'created_utc': submission.created_utc
                    })
        except Exception:
            continue

    return memes
