#!/usr/bin/env python3
"""
Database migration script to add new workflow fields to existing memes.
Run this script once to update the database schema.
"""

import os
import sys
from sqlalchemy import text

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Meme

def migrate_database():
    """Add new workflow fields to existing memes"""
    app = create_app()

    with app.app_context():
        try:
            # Check if the new columns already exist
            with db.engine.connect() as connection:
                result = connection.execute(text("PRAGMA table_info(meme)"))
                columns = [row[1] for row in result]

                # Add missing columns
                if 'subreddit' not in columns:
                    print("Adding 'subreddit' column...")
                    connection.execute(text("ALTER TABLE meme ADD COLUMN subreddit VARCHAR(100) DEFAULT 'memes'"))
                    # Update existing memes to have a default subreddit
                    connection.execute(text("UPDATE meme SET subreddit = 'memes' WHERE subreddit IS NULL"))
                    connection.commit()

                if 'text_approved' not in columns:
                    print("Adding 'text_approved' column...")
                    connection.execute(text("ALTER TABLE meme ADD COLUMN text_approved BOOLEAN DEFAULT 0"))
                    connection.commit()

                if 'audio_approved' not in columns:
                    print("Adding 'audio_approved' column...")
                    connection.execute(text("ALTER TABLE meme ADD COLUMN audio_approved BOOLEAN DEFAULT 0"))
                    connection.commit()

                if 'video_approved' not in columns:
                    print("Adding 'video_approved' column...")
                    connection.execute(text("ALTER TABLE meme ADD COLUMN video_approved BOOLEAN DEFAULT 0"))
                    connection.commit()

            print("Database migration completed successfully!")

        except Exception as e:
            print(f"Error during migration: {e}")
            return False

    return True

if __name__ == "__main__":
    print("Starting database migration...")
    if migrate_database():
        print("Migration completed successfully!")
    else:
        print("Migration failed!")
        sys.exit(1)
