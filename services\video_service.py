import os
import logging
import requests
from PIL import Image
import numpy as np

# Import video generation libraries
try:
    import cv2
    OPENCV_AVAILABLE = True
    logging.info("OpenCV is available for video generation")
except ImportError:
    OPENCV_AVAILABLE = False
    logging.warning("OpenCV is not available - video generation will be limited")

def get_audio_duration(audio_path):
    """Get audio duration in seconds"""
    try:
        if not audio_path or not os.path.exists(audio_path):
            return 3.0  # Default duration for image-only videos

        # Rough estimation based on file size (WAV files are typically ~16KB per second)
        file_size = os.path.getsize(audio_path)
        estimated_duration = max(3.0, min(15.0, file_size / 16000))
        logging.info(f"Estimated audio duration: {estimated_duration:.2f} seconds for {audio_path}")
        return estimated_duration
    except Exception as e:
        logging.error(f"Error estimating audio duration: {e}")
        return 3.0

def download_image_from_url(url, local_path):
    """Download image from URL to local path"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()

        with open(local_path, 'wb') as f:
            f.write(response.content)

        logging.info(f"Downloaded image from {url} to {local_path}")
        return True
    except Exception as e:
        logging.error(f"Error downloading image from {url}: {e}")
        return False

def create_clip_with_opencv(meme, audio_path, output_path):
    """Create clip using OpenCV with enhanced error handling and debugging"""
    try:
        logging.info(f"Starting clip creation for meme {meme.id}")

        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Handle image source - try local first, then download from URL
        image_source = None

        if meme.image_path and os.path.exists(meme.image_path):
            image_source = meme.image_path
            logging.info(f"Using local image: {image_source}")
        elif meme.url:
            # Try to download image from URL
            temp_image_path = os.path.join('static', 'temp', f"temp_{meme.id}.jpg")
            os.makedirs(os.path.dirname(temp_image_path), exist_ok=True)

            if download_image_from_url(meme.url, temp_image_path):
                image_source = temp_image_path
                logging.info(f"Downloaded and using image: {image_source}")
            else:
                logging.error(f"Failed to download image from {meme.url}")
                return False
        else:
            logging.error(f"No image source available for meme {meme.id}")
            return False

        # Load and validate image
        img = cv2.imread(image_source)
        if img is None:
            logging.error(f"Failed to load image from {image_source}")
            return False

        height, width, channels = img.shape
        logging.info(f"Image loaded: {width}x{height}x{channels}")

        # Resize to standard clip size (720p)
        target_height = 720
        target_width = int(width * (target_height / height))

        # Ensure width is even (required for some codecs)
        if target_width % 2 != 0:
            target_width += 1

        img_resized = cv2.resize(img, (target_width, target_height))
        logging.info(f"Image resized to: {target_width}x{target_height}")

        # Determine clip duration
        clip_duration = get_audio_duration(audio_path)
        logging.info(f"Clip duration: {clip_duration} seconds")

        # Create clip writer with better codec settings
        fps = 30  # Higher FPS for smoother clip
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Try mp4v first
        clip_writer = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))

        if not clip_writer.isOpened():
            # Try alternative codec
            logging.warning("mp4v codec failed, trying XVID")
            fourcc = cv2.VideoWriter_fourcc(*'XVID')
            clip_writer = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))

            if not clip_writer.isOpened():
                logging.error("Failed to initialize clip writer with any codec")
                return False

        # Write frames (static image for duration)
        total_frames = int(clip_duration * fps)
        logging.info(f"Writing {total_frames} frames at {fps} FPS")

        for frame_num in range(total_frames):
            clip_writer.write(img_resized)

            # Log progress every 30 frames
            if frame_num % 30 == 0:
                logging.info(f"Written {frame_num}/{total_frames} frames")

        # Release clip writer
        clip_writer.release()

        # Clean up temporary image if we downloaded it
        if image_source != meme.image_path and os.path.exists(image_source):
            try:
                os.remove(image_source)
                logging.info(f"Cleaned up temporary image: {image_source}")
            except:
                pass

        # Verify clip file was created
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
            logging.info(f"Clip created successfully: {output_path} ({os.path.getsize(output_path)} bytes)")
            return True
        else:
            logging.error(f"Clip file was not created or is too small: {output_path}")
            return False

    except Exception as e:
        logging.error(f"Error creating clip with OpenCV: {e}")
        return False

def create_clip_from_meme(meme, audio_path, output_path):
    """Create clip from meme image and audio with comprehensive error handling"""
    logging.info(f"Creating clip for meme {meme.id}: {output_path}")

    # Use OpenCV for clip generation if available
    if OPENCV_AVAILABLE:
        success = create_clip_with_opencv(meme, audio_path, output_path)
        if success:
            logging.info(f"Successfully created clip using OpenCV: {output_path}")
            return True
        else:
            logging.error(f"Failed to create clip using OpenCV for meme {meme.id}")
            return False

    # Fallback: Create placeholder if OpenCV not available
    logging.warning("OpenCV not available, creating placeholder file")
    try:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        placeholder_path = output_path.replace('.mp4', '_placeholder.txt')
        with open(placeholder_path, 'w') as f:
            f.write(f"Clip placeholder for meme ID: {meme.id}\n")
            f.write(f"Meme URL: {meme.url}\n")
            f.write(f"Audio file: {audio_path}\n")
            f.write(f"Text: {meme.text}\n")
            f.write(f"Image path: {meme.image_path}\n")
            f.write("Install OpenCV (pip install opencv-python) to generate actual clips.\n")

        logging.info(f"Created placeholder file: {placeholder_path}")
        return True
    except Exception as e:
        logging.error(f"Failed to create placeholder file: {e}")
        return False
