/* Dark Meme Styles */
.meme, .meme-editor {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.meme:hover, .meme-editor:hover {
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    transform: translateY(-2px);
    border-color: var(--border-accent);
}

.meme img, .meme-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.meme-text {
    margin: 1rem 0;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    border: 1px solid var(--border-primary);
}

.meme-text strong {
    color: var(--primary-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.875rem;
}

.meme-text p {
    margin-top: 0.5rem;
    color: var(--text-secondary);
    font-style: italic;
}

.meme-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.meme-text-editor {
    margin-top: 1rem;
}

.meme-text-editor textarea {
    min-height: 100px;
    resize: vertical;
}

/* Meme Grid Layout */
.memes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.meme-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.meme-card:hover {
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    transform: translateY(-2px);
    border-color: var(--border-accent);
}

.meme-image {
    position: relative;
    overflow: hidden;
}

.meme-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.meme-card:hover .meme-image img {
    transform: scale(1.05);
}

.meme-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    padding: 1rem;
}

.meme-card:hover .meme-overlay {
    opacity: 1;
}

.meme-overlay-text {
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
}

.meme-content {
    padding: 1.5rem;
}

.meme-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.meme-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.meme-subreddit {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-weight: 500;
}

.meme-score {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.meme-text-preview {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 3px solid var(--secondary-color);
    border: 1px solid var(--border-primary);
}

.meme-text-preview.empty {
    border-left-color: var(--warning-color);
    background: rgba(251, 191, 36, 0.1);
    border-color: var(--warning-color);
}

.meme-text-preview p {
    margin: 0;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.875rem;
}

.meme-text-preview.empty p {
    color: var(--warning-color);
}

/* Meme Editor Specific */
.meme-editor-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.meme-editor-textarea {
    min-height: 120px;
    resize: vertical;
    font-family: inherit;
}

.meme-editor-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Meme Status Indicators */
.meme-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.meme-status.ready {
    background: rgba(52, 211, 153, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.meme-status.needs-text {
    background: rgba(251, 191, 36, 0.2);
    color: var(--warning-color);
    border: 1px solid var(--warning-color);
}

.meme-status.processing {
    background: rgba(139, 92, 246, 0.2);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

/* Bulk Actions */
.bulk-actions {
    background: var(--bg-card);
    padding: 1rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-primary);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.bulk-select {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.bulk-actions-buttons {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
}

/* Checkbox Styles */
input[type="checkbox"] {
    width: auto;
    margin-right: 0.5rem;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .memes-grid {
        grid-template-columns: 1fr;
    }

    .meme-actions {
        flex-direction: column;
    }

    .meme-editor-actions {
        flex-direction: column;
    }

    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .bulk-actions-buttons {
        margin-left: 0;
        justify-content: center;
    }

    .meme-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}
