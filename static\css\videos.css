/* Dark Video and Audio Styles */
.video-item, .audio-item {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.video-item:hover, .audio-item:hover {
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    transform: translateY(-2px);
    border-color: var(--border-accent);
}

.video-player video, .audio-player audio {
    width: 100%;
    max-width: 600px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 1rem;
}

.video-info, .audio-info {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--secondary-color);
    border: 1px solid var(--border-primary);
}

.video-actions, .audio-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

/* Video Grid Layout */
.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.video-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.video-card:hover {
    box-shadow: var(--shadow-lg), var(--shadow-glow);
    transform: translateY(-2px);
    border-color: var(--border-accent);
}

.video-thumbnail {
    position: relative;
    background: var(--gray-100);
    height: 225px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.video-thumbnail video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-play-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-play-overlay:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-content {
    padding: 1.5rem;
}

.video-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.video-duration {
    background: var(--gray-800);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.video-text-preview {
    background: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 3px solid var(--secondary-color);
}

.video-text-preview p {
    margin: 0;
    color: var(--gray-600);
    font-style: italic;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Audio Specific Styles */
.audio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.audio-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.audio-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.audio-player-container {
    background: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.audio-waveform {
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.audio-waveform::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent 0px,
        transparent 2px,
        rgba(255, 255, 255, 0.3) 2px,
        rgba(255, 255, 255, 0.3) 4px
    );
    animation: waveform 2s linear infinite;
}

@keyframes waveform {
    0% { transform: translateX(-20px); }
    100% { transform: translateX(20px); }
}

/* Video Generation Status */
.generation-status {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.status-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.status-icon.processing {
    background: var(--primary-color);
    animation: pulse 2s infinite;
}

.status-icon.completed {
    background: var(--secondary-color);
}

.status-icon.error {
    background: var(--danger-color);
}

.status-text {
    flex: 1;
}

.status-title {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.status-description {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.progress-container {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.75rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
    text-align: center;
}

/* Download Actions */
.download-section {
    background: var(--gray-100);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
    text-align: center;
}

.download-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.download-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--secondary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: var(--secondary-dark);
    transform: translateY(-1px);
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .videos-grid {
        grid-template-columns: 1fr;
    }

    .audio-grid {
        grid-template-columns: 1fr;
    }

    .video-actions, .audio-actions {
        flex-direction: column;
    }

    .download-buttons {
        flex-direction: column;
    }

    .video-meta {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .status-header {
        flex-direction: column;
        text-align: center;
    }
}
