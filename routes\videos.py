from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, Video, db
from services.helpers import require_login
from services.audio_service import generate_audio_from_text
from services.video_service import create_video_from_meme
import os
import uuid

videos_bp = Blueprint('videos', __name__)

@videos_bp.route('/generate_videos')
@require_login
def generate_videos():
    """Generate videos from approved memes"""
    user_id = session['user_id']

    # Only generate videos for memes that have been approved through the workflow
    # This includes both text-approved memes (with or without audio) and audio-approved memes
    memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True
    ).all()

    generated_count = 0
    for meme in memes:
        try:
            # Skip if video already exists for this meme
            existing_video = Video.query.filter_by(meme_id=meme.id, discarded=False).first()
            if existing_video:
                continue

            # Generate unique filenames
            video_filename = f"video_{meme.id}_{uuid.uuid4().hex[:8]}.mp4"
            video_path = os.path.join('static', 'videos', video_filename)

            audio_path = None

            # Check if meme has text and is approved for audio
            if meme.text and meme.text.strip() and meme.audio_approved:
                # Use existing audio path from session if available
                session_audio_key = f'audio_path_{meme.id}'
                if session_audio_key in session:
                    audio_path = session[session_audio_key]
                    # Verify the file still exists
                    if not os.path.exists(audio_path):
                        # Generate new audio if file doesn't exist
                        audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                        audio_path = os.path.join('static', 'audio', audio_filename)
                        if not generate_audio_from_text(meme.text, audio_path):
                            audio_path = None
                else:
                    # Generate new audio
                    audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                    audio_path = os.path.join('static', 'audio', audio_filename)
                    if not generate_audio_from_text(meme.text, audio_path):
                        audio_path = None

            # Generate video (with or without audio)
            if create_video_from_meme(meme, audio_path, video_path):
                # Save video record to database
                video_record = Video(
                    user_id=user_id,
                    meme_id=meme.id,
                    video_path=video_path,
                    audio_path=audio_path,
                    text=meme.text or ""
                )
                db.session.add(video_record)
                generated_count += 1

        except Exception:
            continue

    db.session.commit()

    session['flash_message'] = f"Generated {generated_count} videos successfully!"
    return redirect(url_for('videos.review_videos'))

@videos_bp.route('/review_audio')
@require_login
def review_audio():
    """Review generated audio files"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()
    return render_template('review_audio.html', videos=videos)

@videos_bp.route('/review_videos')
@require_login
def review_videos():
    """Review generated videos organized by subreddit"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()

    # Group videos by subreddit
    videos_by_subreddit = {}
    for video in videos:
        # Get the meme to access subreddit information
        meme = Meme.query.get(video.meme_id)
        if meme:
            subreddit = meme.subreddit
            if subreddit not in videos_by_subreddit:
                videos_by_subreddit[subreddit] = []
            videos_by_subreddit[subreddit].append(video)

    return render_template('review_videos.html', videos_by_subreddit=videos_by_subreddit)

@videos_bp.route('/discard_video', methods=['POST'])
@require_login
def discard_video():
    """Discard a video"""
    user_id = session['user_id']
    video_id = request.form.get('video_id')

    video = Video.query.filter_by(id=video_id, user_id=user_id).first()
    if video:
        video.discarded = True
        db.session.commit()

    return redirect(url_for('videos.review_videos'))
