from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, Video, db
from services.helpers import require_login
from services.audio_service import generate_audio_from_text
from services.video_service import create_video_from_meme
import os
import uuid

videos_bp = Blueprint('videos', __name__)

@videos_bp.route('/generate_videos')
@require_login
def generate_videos():
    """Generate videos from approved memes with enhanced error handling and debugging"""
    import logging

    user_id = session['user_id']

    logging.info(f"Starting video generation for user {user_id}")

    # Only generate videos for memes that have been approved through the workflow
    # This includes both text-approved memes (with or without audio) and audio-approved memes
    memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True
    ).all()

    logging.info(f"Found {len(memes)} text-approved memes for video generation")

    generated_count = 0
    failed_count = 0
    skipped_count = 0
    error_messages = []

    for meme in memes:
        try:
            # Skip if video already exists for this meme
            existing_video = Video.query.filter_by(meme_id=meme.id, discarded=False).first()
            if existing_video:
                logging.info(f"Skipping meme {meme.id} - video already exists")
                skipped_count += 1
                continue

            logging.info(f"Processing meme {meme.id} for video generation")

            # Generate unique filenames
            video_filename = f"video_{meme.id}_{uuid.uuid4().hex[:8]}.mp4"
            video_path = os.path.join('static', 'videos', video_filename)

            # Ensure videos directory exists
            os.makedirs(os.path.dirname(video_path), exist_ok=True)

            audio_path = None

            # Check if meme has text and is approved for audio
            if meme.text and meme.text.strip() and meme.audio_approved:
                logging.info(f"Meme {meme.id} has text and audio approval - setting up audio")

                # Use existing audio path from session if available
                session_audio_key = f'audio_path_{meme.id}'
                if session_audio_key in session:
                    audio_path = session[session_audio_key]
                    logging.info(f"Found audio path in session: {audio_path}")

                    # Verify the file still exists
                    if not os.path.exists(audio_path):
                        logging.warning(f"Audio file not found, regenerating: {audio_path}")
                        # Generate new audio if file doesn't exist
                        audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                        audio_path = os.path.join('static', 'audio', audio_filename)
                        os.makedirs(os.path.dirname(audio_path), exist_ok=True)

                        if not generate_audio_from_text(meme.text, audio_path):
                            logging.error(f"Failed to regenerate audio for meme {meme.id}")
                            audio_path = None
                else:
                    logging.info(f"No audio in session, generating new audio for meme {meme.id}")
                    # Generate new audio
                    audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                    audio_path = os.path.join('static', 'audio', audio_filename)
                    os.makedirs(os.path.dirname(audio_path), exist_ok=True)

                    if not generate_audio_from_text(meme.text, audio_path):
                        logging.error(f"Failed to generate audio for meme {meme.id}")
                        audio_path = None
            else:
                logging.info(f"Meme {meme.id} will be image-only (no text or not audio approved)")

            # Generate video (with or without audio)
            logging.info(f"Creating video for meme {meme.id}: {video_path}")

            if create_video_from_meme(meme, audio_path, video_path):
                # Save video record to database
                video_record = Video(
                    user_id=user_id,
                    meme_id=meme.id,
                    video_path=video_path,
                    audio_path=audio_path,
                    text=meme.text or ""
                )
                db.session.add(video_record)
                generated_count += 1
                logging.info(f"Successfully created video for meme {meme.id}")
            else:
                failed_count += 1
                error_msg = f"Failed to create video for meme {meme.id}"
                error_messages.append(error_msg)
                logging.error(error_msg)

        except Exception as e:
            failed_count += 1
            error_msg = f"Error processing meme {meme.id}: {str(e)}"
            error_messages.append(error_msg)
            logging.error(error_msg)
            continue

    # Commit database changes
    try:
        db.session.commit()
        logging.info("Database changes committed successfully")
    except Exception as e:
        logging.error(f"Error committing database changes: {e}")
        db.session.rollback()

    # Prepare user feedback message
    if generated_count > 0:
        message = f"Generated {generated_count} videos successfully!"
        if skipped_count > 0:
            message += f" Skipped {skipped_count} existing videos."
        if failed_count > 0:
            message += f" {failed_count} videos failed to generate."
    elif skipped_count > 0:
        message = f"All {skipped_count} videos already exist. No new videos generated."
    elif failed_count > 0:
        message = f"Failed to generate {failed_count} videos. Check logs for details."
    else:
        message = "No videos to generate. Make sure you have text-approved memes."

    session['flash_message'] = message
    logging.info(f"Video generation completed: {message}")

    return redirect(url_for('videos.review_videos'))

@videos_bp.route('/review_audio')
@require_login
def review_audio():
    """Review generated audio files"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()
    return render_template('review_audio.html', videos=videos)

@videos_bp.route('/review_videos')
@require_login
def review_videos():
    """Review generated videos organized by subreddit"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()

    # Group videos by subreddit
    videos_by_subreddit = {}
    for video in videos:
        # Get the meme to access subreddit information
        meme = Meme.query.get(video.meme_id)
        if meme:
            subreddit = meme.subreddit
            if subreddit not in videos_by_subreddit:
                videos_by_subreddit[subreddit] = []
            videos_by_subreddit[subreddit].append(video)

    return render_template('review_videos.html', videos_by_subreddit=videos_by_subreddit)

@videos_bp.route('/discard_video', methods=['POST'])
@require_login
def discard_video():
    """Discard a video"""
    user_id = session['user_id']
    video_id = request.form.get('video_id')

    video = Video.query.filter_by(id=video_id, user_id=user_id).first()
    if video:
        video.discarded = True
        db.session.commit()

    return redirect(url_for('videos.review_videos'))
